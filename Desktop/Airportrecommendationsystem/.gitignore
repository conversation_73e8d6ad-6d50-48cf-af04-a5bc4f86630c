# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# API Keys (security)
**/api_key.txt
**/config.ini
**/*_key.py

# Data files (large datasets)
raw_data/
processed_data/
preprocessed_data/

# Log files
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Streamlit
.streamlit/

# Large CSV files (keep sample data only)
*.csv
!sample_data/sample_*.csv

# Pickle files (preprocessed data)
*.pkl
*.pickle

# Compressed files
*.zip
*.tar.gz
*.rar

# Model files (if any ML models are added later)
*.model
*.pkl
*.joblib
*.h5

# Documentation build
docs/_build/

# PyCharm
.idea/
