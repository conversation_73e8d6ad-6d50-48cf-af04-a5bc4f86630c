[2025-06-30 14:03:43,120] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:03:43,120] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:03:43,121] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:03:43,121] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:03:45,412] INFO - Saved 100 records to raw_data/delhi_to_domestic_2025-06-30.csv
[2025-06-30 14:03:45,413] INFO - Completed: fetch_flights_from_api in 2.29 seconds

[2025-06-30 14:03:45,466] INFO - Saved 100 records to raw_data/international_to_delhi_2025-06-30.csv
[2025-06-30 14:03:45,467] INFO - Completed: fetch_flights_from_api in 2.35 seconds

[2025-06-30 14:03:45,472] INFO - Saved 100 records to raw_data/delhi_to_international_2025-06-30.csv
[2025-06-30 14:03:45,472] INFO - Completed: fetch_flights_from_api in 2.35 seconds

[2025-06-30 14:03:45,512] INFO - Saved 100 records to raw_data/domestic_to_delhi_2025-06-30.csv
[2025-06-30 14:03:45,513] INFO - Completed: fetch_flights_from_api in 2.39 seconds

[2025-06-30 14:03:45,513] INFO - Starting: daily_ingestion
[2025-06-30 14:03:45,541] INFO - Ingested and saved 400 rows to processed_data/all_flights_2025-06-30.csv
[2025-06-30 14:03:45,541] INFO - Completed: daily_ingestion in 0.03 seconds

[2025-06-30 14:03:45,541] INFO - Starting: load_to_postgres
[2025-06-30 14:03:45,543] INFO - Loaded DataFrame with 400 rows
[2025-06-30 14:04:27,675] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:04:27,675] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:04:27,675] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:04:27,675] INFO - Starting: fetch_flights_from_api
[2025-06-30 14:04:29,915] INFO - Saved 100 records to raw_data/international_to_delhi_2025-06-30.csv
[2025-06-30 14:04:29,916] INFO - Completed: fetch_flights_from_api in 2.24 seconds

[2025-06-30 14:04:29,961] INFO - Saved 100 records to raw_data/delhi_to_international_2025-06-30.csv
[2025-06-30 14:04:29,962] INFO - Completed: fetch_flights_from_api in 2.29 seconds

[2025-06-30 14:04:30,062] INFO - Saved 100 records to raw_data/delhi_to_domestic_2025-06-30.csv
[2025-06-30 14:04:30,063] INFO - Completed: fetch_flights_from_api in 2.39 seconds

[2025-06-30 14:04:30,071] INFO - Saved 100 records to raw_data/domestic_to_delhi_2025-06-30.csv
[2025-06-30 14:04:30,072] INFO - Completed: fetch_flights_from_api in 2.4 seconds

[2025-06-30 14:04:30,072] INFO - Starting: daily_ingestion
[2025-06-30 14:04:30,101] INFO - Ingested and saved 400 rows to processed_data/all_flights_2025-06-30.csv
[2025-06-30 14:04:30,101] INFO - Completed: daily_ingestion in 0.03 seconds

[2025-06-30 14:04:30,101] INFO - Starting: load_to_postgres
[2025-06-30 14:04:30,103] INFO - Loaded DataFrame with 400 rows
[2025-06-30 14:04:31,721] INFO - [✓] Loaded 400 rows to PostgreSQL table 'flights'.
[2025-06-30 14:04:31,722] INFO - Completed: load_to_postgres in 1.62 seconds

[2025-07-01 16:32:59,604] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:32:59,604] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:32:59,604] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:32:59,604] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:33:01,371] INFO - Saved 100 records to raw_data/domestic_to_delhi_2025-07-01.csv
[2025-07-01 16:33:01,371] INFO - Completed: fetch_flights_from_api in 1.77 seconds

[2025-07-01 16:33:01,374] INFO - Saved 100 records to raw_data/international_to_delhi_2025-07-01.csv
[2025-07-01 16:33:01,374] INFO - Completed: fetch_flights_from_api in 1.77 seconds

[2025-07-01 16:33:01,416] INFO - Saved 100 records to raw_data/delhi_to_domestic_2025-07-01.csv
[2025-07-01 16:33:01,416] INFO - Completed: fetch_flights_from_api in 1.81 seconds

[2025-07-01 16:33:01,432] INFO - Saved 100 records to raw_data/delhi_to_international_2025-07-01.csv
[2025-07-01 16:33:01,433] INFO - Completed: fetch_flights_from_api in 1.83 seconds

[2025-07-01 16:33:01,433] INFO - Starting: daily_ingestion
[2025-07-01 16:33:01,460] INFO - Ingested and saved 400 rows to processed_data/all_flights_2025-07-01.csv
[2025-07-01 16:33:01,460] INFO - Completed: daily_ingestion in 0.03 seconds

[2025-07-01 16:33:01,461] INFO - Starting: load_to_postgres
[2025-07-01 16:33:01,463] INFO - Loaded DataFrame with 400 rows
[2025-07-01 16:33:01,973] INFO - [✓] Loaded 400 rows to PostgreSQL table 'flights'.
[2025-07-01 16:33:01,973] INFO - Completed: load_to_postgres in 0.51 seconds

[2025-07-01 16:40:10,439] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:40:10,439] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:40:10,439] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:40:10,439] INFO - Starting: fetch_flights_from_api
[2025-07-01 16:40:12,151] INFO - Saved 100 records to raw_data/domestic_to_delhi_2025-07-01.csv
[2025-07-01 16:40:12,151] INFO - Completed: fetch_flights_from_api in 1.71 seconds

[2025-07-01 16:40:12,318] INFO - Saved 100 records to raw_data/delhi_to_domestic_2025-07-01.csv
[2025-07-01 16:40:12,319] INFO - Completed: fetch_flights_from_api in 1.88 seconds

[2025-07-01 16:40:12,380] INFO - Saved 100 records to raw_data/international_to_delhi_2025-07-01.csv
[2025-07-01 16:40:12,381] INFO - Completed: fetch_flights_from_api in 1.94 seconds

[2025-07-01 16:40:12,446] INFO - Saved 100 records to raw_data/delhi_to_international_2025-07-01.csv
[2025-07-01 16:40:12,447] INFO - Completed: fetch_flights_from_api in 2.01 seconds

[2025-07-01 16:40:12,447] INFO - Starting: daily_ingestion
[2025-07-01 16:40:12,478] INFO - Ingested and saved 400 rows to processed_data/all_flights_2025-07-01.csv
[2025-07-01 16:40:12,478] INFO - Completed: daily_ingestion in 0.03 seconds

[2025-07-01 16:40:12,479] INFO - Starting: load_to_postgres
[2025-07-01 16:40:12,481] INFO - Loaded DataFrame with 400 rows
[2025-07-01 16:40:12,633] INFO - [✓] Loaded 400 rows to PostgreSQL table 'flights'.
[2025-07-01 16:40:12,633] INFO - Completed: load_to_postgres in 0.15 seconds

[2025-07-03 16:20:51,126] INFO - Starting: fetch_flights_from_api
[2025-07-03 16:20:51,126] INFO - Starting: fetch_flights_from_api
[2025-07-03 16:20:51,126] INFO - Starting: fetch_flights_from_api
[2025-07-03 16:20:51,126] INFO - Starting: fetch_flights_from_api
[2025-07-03 16:20:52,858] INFO - Saved 100 records to raw_data/domestic_to_delhi_2025-07-03.csv
[2025-07-03 16:20:52,859] INFO - Completed: fetch_flights_from_api in 1.73 seconds

[2025-07-03 16:20:52,976] INFO - Saved 100 records to raw_data/delhi_to_domestic_2025-07-03.csv
[2025-07-03 16:20:52,976] INFO - Completed: fetch_flights_from_api in 1.85 seconds

[2025-07-03 16:20:53,016] INFO - Saved 100 records to raw_data/delhi_to_international_2025-07-03.csv
[2025-07-03 16:20:53,016] INFO - Completed: fetch_flights_from_api in 1.89 seconds

[2025-07-03 16:20:53,137] INFO - Saved 100 records to raw_data/international_to_delhi_2025-07-03.csv
[2025-07-03 16:20:53,137] INFO - Completed: fetch_flights_from_api in 2.01 seconds

[2025-07-03 16:20:53,138] INFO - Starting: daily_ingestion
[2025-07-03 16:20:53,160] INFO - Ingested and saved 400 rows to processed_data/all_flights_2025-07-03.csv
[2025-07-03 16:20:53,160] INFO - Completed: daily_ingestion in 0.02 seconds

[2025-07-03 16:20:53,160] INFO - Starting: load_to_postgres
[2025-07-03 16:20:53,163] INFO - Loaded DataFrame with 400 rows
[2025-07-03 16:20:53,879] INFO - [✓] Loaded 400 rows to PostgreSQL table 'flights'.
[2025-07-03 16:20:53,879] INFO - Completed: load_to_postgres in 0.72 seconds

